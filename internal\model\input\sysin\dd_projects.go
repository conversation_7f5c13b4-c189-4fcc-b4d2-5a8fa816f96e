// Package sysin
// @AutoGenerate Version 2.16.10
package sysin

import (
	"context"
	"intellos/internal/library/hgorm/hook"
	"intellos/internal/model/entity"
	"intellos/internal/model/input/form"

	"github.com/gogf/gf/v2/os/gtime"
)

// DdProjectsUpdateFields 修改调度系统同步最新评委会接口字段过滤
type DdProjectsUpdateFields struct {
	RoomId         string `json:"roomId"         dc:"房间id"`
	RoomName       string `json:"roomName"       dc:"房间名称"`
	BidCouncilId   string `json:"bidCouncilId"   dc:"评委会id"`
	BidCouncilName string `json:"bidCouncilName" dc:"评委会名称"`
	ProjectStatus  string `json:"projectStatus"  dc:"项目状态"`
	UpdatedBy      int64  `json:"updatedBy"      dc:"更新者"`
}

// DdProjectsInsertFields 新增调度系统同步最新评委会接口字段过滤
type DdProjectsInsertFields struct {
	RoomId         string `json:"roomId"         dc:"房间id"`
	RoomName       string `json:"roomName"       dc:"房间名称"`
	BidCouncilId   string `json:"bidCouncilId"   dc:"评委会id"`
	BidCouncilName string `json:"bidCouncilName" dc:"评委会名称"`
	ProjectStatus  string `json:"projectStatus"  dc:"项目状态"`
	CreatedBy      int64  `json:"createdBy"      dc:"创建者"`
}

// DdProjectsEditInp 修改/新增调度系统同步最新评委会接口
type DdProjectsEditInp struct {
	entity.DdProjects
}

func (in *DdProjectsEditInp) Filter(ctx context.Context) (err error) {

	return
}

type DdProjectsEditModel struct{}

// DdProjectsDeleteInp 删除调度系统同步最新评委会接口
type DdProjectsDeleteInp struct {
	Id interface{} `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DdProjectsDeleteInp) Filter(ctx context.Context) (err error) {
	return
}

type DdProjectsDeleteModel struct{}

// DdProjectsViewInp 获取指定调度系统同步最新评委会接口信息
type DdProjectsViewInp struct {
	Id int64 `json:"id" v:"required#ID不能为空" dc:"ID"`
}

func (in *DdProjectsViewInp) Filter(ctx context.Context) (err error) {
	return
}

type DdProjectsViewModel struct {
	entity.DdProjects
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
	UpdatedBySumma *hook.MemberSumma `json:"updatedBySumma" dc:"更新者摘要信息"`
}

// DdProjectsListInp 获取调度系统同步最新评委会接口列表
type DdProjectsListInp struct {
	form.PageReq
	Id           int64         `json:"id"           dc:"ID"`
	RoomName     string        `json:"roomName"     dc:"房间名称"`
	BidCouncilId string        `json:"bidCouncilId" dc:"评委会id"`
	CreatedAt    []*gtime.Time `json:"createdAt"    dc:"创建时间"`
}

func (in *DdProjectsListInp) Filter(ctx context.Context) (err error) {
	return
}

type DdProjectsListModel struct {
	Id             int64             `json:"id"             dc:"ID"`
	RoomId         string            `json:"roomId"         dc:"房间id"`
	RoomName       string            `json:"roomName"       dc:"房间名称"`
	BidCouncilId   string            `json:"bidCouncilId"   dc:"评委会id"`
	BidCouncilName string            `json:"bidCouncilName" dc:"评委会名称"`
	ProjectStatus  string            `json:"projectStatus"  dc:"项目状态"`
	CreatedBy      int64             `json:"createdBy"      dc:"创建者"`
	CreatedBySumma *hook.MemberSumma `json:"createdBySumma" dc:"创建者摘要信息"`
	UpdatedBy      int64             `json:"updatedBy"      dc:"更新者"`
	UpdatedBySumma *hook.MemberSumma `json:"updatedBySumma" dc:"更新者摘要信息"`
	CreatedAt      *gtime.Time       `json:"createdAt"      dc:"创建时间"`
	UpdatedAt      *gtime.Time       `json:"updatedAt"      dc:"修改时间"`
}

// DdProjectsExportModel 导出调度系统同步最新评委会接口
type DdProjectsExportModel struct {
	Id             int64       `json:"id"             dc:"ID"`
	RoomId         string      `json:"roomId"         dc:"房间id"`
	RoomName       string      `json:"roomName"       dc:"房间名称"`
	BidCouncilId   string      `json:"bidCouncilId"   dc:"评委会id"`
	BidCouncilName string      `json:"bidCouncilName" dc:"评委会名称"`
	ProjectStatus  string      `json:"projectStatus"  dc:"项目状态"`
	CreatedBy      int64       `json:"createdBy"      dc:"创建者"`
	UpdatedBy      int64       `json:"updatedBy"      dc:"更新者"`
	CreatedAt      *gtime.Time `json:"createdAt"      dc:"创建时间"`
	UpdatedAt      *gtime.Time `json:"updatedAt"      dc:"修改时间"`
}