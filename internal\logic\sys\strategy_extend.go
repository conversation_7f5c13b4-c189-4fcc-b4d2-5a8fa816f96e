package sys

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"intellos/internal/dao"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
)

// SysStrategyExtend 实现扩展接口
type SysStrategyExtend struct{}

// New 创建实例（供外部调用）
func NewSysStrategyExtend() service.ISysStrategyExtend {
	return &SysStrategyExtend{}
}

// RelationEdit 实现：修改/新增策略配置关联
func (s *SysStrategyExtend) RelationEdit(ctx context.Context, in *sysin.StrategyRelationEditInp) (err error) {
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		//处理区域关联 先删除，再保存
		if len(in.StrategyArea) > 0 {
			//先删除原来关联
			if _, err = dao.StrategyAreaRelation.Ctx(ctx).Where(dao.StrategyAreaRelation.Columns().StrategyId, in.StrategyArea[0].StrategyId).Delete(); err != nil {
				err = gerror.Wrap(err, "删除原有区域关联失败，请稍后重试！")
				return
			}
			for _, area := range in.StrategyArea {
				//验证区域是否存在
				if _, err = dao.AreaInfo.Ctx(ctx).WherePri(area.AreaId).One(); err != nil {
					err = gerror.Wrap(err, "区域不存在，请检查后重试！")
					return
				}
				//保存
				if _, err = dao.StrategyAreaRelation.Ctx(ctx).Insert(area); err != nil {
					err = gerror.Wrap(err, "保存区域关联失败，请稍后重试！")
					return
				}
			}
		}
		//处理策略条件关联 只uodate
		if len(in.StrategyCondition) > 0 {
			for _, condition := range in.StrategyCondition {
				//验证条件是否存在
				if _, err = dao.StrategyCondition.Ctx(ctx).WherePri(condition.Id).One(); err != nil {
					err = gerror.Wrap(err, "策略条件不存在，请检查后重试！")
					return
				}
				//更新
				if _, err = dao.StrategyCondition.Ctx(ctx).Data(condition).WherePri(condition.Id).Update(); err != nil {
					err = gerror.Wrap(err, "更新策略条件失败，请稍后重试！")
					return
				}
			}
		}
		return
	})
}
