package service

import (
	"context"
	"intellos/internal/model/input/sysin"
)

type (
	ISysStrategyExtend interface {
		// RelationEdit 修改/新增策略配置关联
		RelationEdit(ctx context.Context, in *sysin.StrategyRelationEditInp) (err error)
	}
)

var (
	localSysStrategyExtend ISysStrategyExtend
)

func SysStrategyExtend() ISysStrategyExtend {
	if localSysStrategyExtend == nil {
		panic("implement not found for interface ISysStrategyRelation, forgot register?")
	}
	return localSysStrategyExtend
}

func RegisterSysStrategyRelation(i ISysStrategyExtend) {
	localSysStrategyExtend = i
}
