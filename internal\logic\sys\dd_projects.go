// Package sys
// @AutoGenerate Version 2.16.10
package sys

import (
	"context"
	"fmt"
	"intellos/internal/dao"
	"intellos/internal/library/contexts"
	"intellos/internal/library/hgorm/handler"
	"intellos/internal/library/hgorm/hook"
	"intellos/internal/model/input/form"
	"intellos/internal/model/input/sysin"
	"intellos/internal/service"
	"intellos/utility/convert"
	"intellos/utility/excel"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

type sSysDdProjects struct{}

func NewSysDdProjects() *sSysDdProjects {
	return &sSysDdProjects{}
}

func init() {
	service.RegisterSysDdProjects(NewSysDdProjects())
}

// Model 调度系统同步最新评委会接口ORM模型
func (s *sSysDdProjects) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.DdProjects.Ctx(ctx), option...)
}

// List 获取调度系统同步最新评委会接口列表
func (s *sSysDdProjects) List(ctx context.Context, in *sysin.DdProjectsListInp) (list []*sysin.DdProjectsListModel, totalCount int, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.Fields(sysin.DdProjectsListModel{})

	// 查询ID
	if in.Id > 0 {
		mod = mod.Where(dao.DdProjects.Columns().Id, in.Id)
	}

	// 查询房间名称
	if in.RoomName != "" {
		mod = mod.Where(dao.DdProjects.Columns().RoomName, in.RoomName)
	}

	// 查询评委会id
	if in.BidCouncilId != "" {
		mod = mod.Where(dao.DdProjects.Columns().BidCouncilId, in.BidCouncilId)
	}

	// 查询创建时间
	if len(in.CreatedAt) == 2 {
		mod = mod.WhereBetween(dao.DdProjects.Columns().CreatedAt, in.CreatedAt[0], in.CreatedAt[1])
	}

	// 分页
	mod = mod.Page(in.Page, in.PerPage)

	// 排序
	mod = mod.OrderDesc(dao.DdProjects.Columns().Id)

	// 操作人摘要信息
	mod = mod.Hook(hook.MemberSummary)

	// 查询数据
	if err = mod.ScanAndCount(&list, &totalCount, false); err != nil {
		err = gerror.Wrap(err, "获取调度系统同步最新评委会接口列表失败，请稍后重试！")
		return
	}
	return
}

// Export 导出调度系统同步最新评委会接口
func (s *sSysDdProjects) Export(ctx context.Context, in *sysin.DdProjectsListInp) (err error) {
	list, totalCount, err := s.List(ctx, in)
	if err != nil {
		return
	}

	// 字段的排序是依据tags的字段顺序，如果你不想使用默认的排序方式，可以直接定义 tags = []string{"字段名称", "字段名称2", ...}
	tags, err := convert.GetEntityDescTags(sysin.DdProjectsExportModel{})
	if err != nil {
		return
	}

	var (
		fileName  = "导出调度系统同步最新评委会接口-" + gctx.CtxId(ctx)
		sheetName = fmt.Sprintf("索引条件共%v行,共%v页,当前导出是第%v页,本页共%v行", totalCount, form.CalPageCount(totalCount, in.PerPage), in.Page, len(list))
		exports   []sysin.DdProjectsExportModel
	)

	if err = gconv.Scan(list, &exports); err != nil {
		return
	}

	err = excel.ExportByStructs(ctx, tags, exports, fileName, sheetName)
	return
}

// Edit 修改/新增调度系统同步最新评委会接口
func (s *sSysDdProjects) Edit(ctx context.Context, in *sysin.DdProjectsEditInp) (err error) {
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {

		// 修改
		if in.Id > 0 {
			in.UpdatedBy = contexts.GetUserId(ctx)
			if _, err = s.Model(ctx).
				Fields(sysin.DdProjectsUpdateFields{}).
				WherePri(in.Id).Data(in).Update(); err != nil {
				err = gerror.Wrap(err, "修改调度系统同步最新评委会接口失败，请稍后重试！")
			}
			return
		}

		// 新增
		in.CreatedBy = contexts.GetUserId(ctx)
		if _, err = s.Model(ctx, &handler.Option{FilterAuth: false}).
			Fields(sysin.DdProjectsInsertFields{}).
			Data(in).OmitEmptyData().Insert(); err != nil {
			err = gerror.Wrap(err, "新增调度系统同步最新评委会接口失败，请稍后重试！")
		}
		return
	})
}

// Delete 删除调度系统同步最新评委会接口
func (s *sSysDdProjects) Delete(ctx context.Context, in *sysin.DdProjectsDeleteInp) (err error) {

	if _, err = s.Model(ctx).WherePri(in.Id).Unscoped().Delete(); err != nil {
		err = gerror.Wrap(err, "删除调度系统同步最新评委会接口失败，请稍后重试！")
		return
	}
	return
}

// View 获取调度系统同步最新评委会接口指定信息
func (s *sSysDdProjects) View(ctx context.Context, in *sysin.DdProjectsViewInp) (res *sysin.DdProjectsViewModel, err error) {
	if err = s.Model(ctx).WherePri(in.Id).Hook(hook.MemberSummary).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取调度系统同步最新评委会接口信息，请稍后重试！")
		return
	}
	return
}